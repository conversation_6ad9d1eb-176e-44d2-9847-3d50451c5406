<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\SmsMessageResource\Pages\CreateSmsMessage;
use App\Filament\Resources\SmsMessageResource\Pages\ListSmsMessages;
use App\Filament\Resources\SmsMessageResource\Pages\ViewSmsMessage;
use App\Jobs\SendSmsTemplateMessageJob;
use App\Models\Guardian;
use App\Models\MessageTemplate;
use App\Models\SmsMessage;
use BackedEnum;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Utilities\Set;
use Filament\Schemas\Schema;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class SmsMessageResource extends Resource
{
    protected static ?string $model = SmsMessage::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-chat-bubble-left';

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                Select::make('template_id')
                    ->options(MessageTemplate::pluck('code', 'id'))
                    ->required()
                    ->translateLabel()
                    ->live()
                    ->afterStateUpdated(function (Set $set, ?string $state) {
                        if (! $state) {
                            return;
                        }

                        $template = MessageTemplate::find($state);
                        $params = $template->params ?? [];

                        // Transform to key => value format for KeyValue component
                        $mapped = collect($params)
                            ->pluck('key')
                            ->mapWithKeys(fn ($key) => [$key => '']) // default empty value
                            ->toArray();

                        $set('params', $mapped);
                    })
                    ->translateLabel()
                    ->label('Template ID'),

                TextInput::make('sender')
                    ->label('Sender')
                    ->translateLabel()
                    ->required(),

                Select::make('payment_type')
                    ->translateLabel()
                    ->options([
                        'subscription' => __('Subscription'),
                        'wallet' => __('Wallet'),
                    ])
                    ->default('subscription')
                    ->required()
                    ->label('Payment Type'),

                Select::make('receiver')
                    ->options(function () {
                        return Guardian::pluck('name', 'phone');
                    })
                    ->preload()
                    ->searchable()
                    ->required()
                    ->translateLabel()
                    ->label('Receiver'),

                KeyValue::make('params')
                    ->label('Template Parameters')
                    ->translateLabel()
                    ->keyLabel('Key')
                    ->valueLabel('Value')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('receiver')
                    ->label('Receiver')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('template_id')
                    ->label('Template')
                    ->translateLabel()
                    ->sortable(),
                TextColumn::make('sender')
                    ->label('Sender ID')
                    ->translateLabel()
                    ->sortable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'sent' => 'info',
                        'delivered' => 'success',
                        'failed' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),
                TextColumn::make('created_at')
                    ->translateLabel()
                    ->dateTime()
                    ->label('Sent At')
                    ->sortable(),
            ])
            ->defaultSort('created_at', 'desc')
            ->recordActions([
                ViewAction::make(),
                /*Tables\Actions\Action::make('resend')
                    ->label('Resend')
                    ->translateLabel()
                    ->action(function (SmsMessage $record) {
                        dispatch(new SendSmsTemplateMessageJob($record->id));
                    }),*/
            ])
            ->toolbarActions([]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListSmsMessages::route('/'),
            'create' => CreateSmsMessage::route('/create'),
            'view' => ViewSmsMessage::route('/{record}'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('SMS Messages');
    }

    public static function getLabel(): string
    {
        return __('SMS Message');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('System Management');
    }
}
